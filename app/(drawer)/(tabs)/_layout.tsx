import { Tabs } from 'expo-router';
import React from 'react';
import { Platform, I18nManager } from 'react-native';
import { useTheme } from 'react-native-paper';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { MenuButton } from '@/components/ui/MenuButton';
import { PaperAppBar } from '@/components/paper';
import { t } from '@/constants/Localization';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const theme = useTheme();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: "absolute",
            backgroundColor: theme.colors.surface,
          },
          default: {
            backgroundColor: theme.colors.surface,
            borderTopColor: theme.colors.outline,
          },
        }),
        tabBarLabelStyle: {
          fontFamily: 'Vazirmatn',
          fontSize: 12,
          fontWeight: '500',
        },
      }}
    >
      <Tabs.Screen
        name="Home"
        options={{
          title: t('home'),
          tabBarIcon: ({ color, focused }) => (
            <IconSymbol
              size={24}
              name={focused ? "house.fill" : "house"}
              color={color}
            />
          ),
          headerShown: true,
          header: () => (
            <PaperAppBar
              title={t('home')}
              showBackAction={false}
              actions={[
                {
                  icon: 'menu',
                  onPress: () => {}, // Will be handled by MenuButton
                  accessibilityLabel: 'باز کردن منو',
                }
              ]}
            />
          ),
          headerLeft: () => <MenuButton />,
        }}
      />
      <Tabs.Screen
        name="Explore"
        options={{
          title: t('explore') || 'کاوش',
          tabBarIcon: ({ color, focused }) => (
            <IconSymbol
              size={24}
              name={focused ? "paperplane.fill" : "paperplane"}
              color={color}
            />
          ),
          headerShown: true,
          header: () => (
            <PaperAppBar
              title={t('explore') || 'کاوش'}
              showBackAction={false}
            />
          ),
        }}
      />
    </Tabs>
  );
}
